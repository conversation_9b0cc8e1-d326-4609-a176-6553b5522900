import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/ar_accessibility_service.dart';

/// A screen that allows users to customize accessibility settings for the AR experience.
class ARAccessibilityScreen extends ConsumerStatefulWidget {
  const ARAccessibilityScreen({super.key});

  @override
  ConsumerState<ARAccessibilityScreen> createState() =>
      _ARAccessibilityScreenState();
}

class _ARAccessibilityScreenState extends ConsumerState<ARAccessibilityScreen> {
  late ARAccessibilityService _accessibilityService;

  // Local state to track changes
  late bool _isHighContrastEnabled;
  late bool _isLargeTextEnabled;
  late bool _isScreenReaderEnabled;
  late bool _isReducedMotionEnabled;
  late bool _isHapticFeedbackEnabled;
  late bool _isAudioGuidanceEnabled;
  late bool _isGestureSimplificationEnabled;

  late double _speechRate;
  late double _speechPitch;
  late String _speechLanguage;

  late Color _primaryAccessibilityColor;
  late Color _secondaryAccessibilityColor;

  // Available speech languages
  final List<Map<String, String>> _availableLanguages = [
    {'code': 'en-US', 'name': 'English (US)'},
    {'code': 'en-GB', 'name': 'English (UK)'},
    {'code': 'es-ES', 'name': 'Spanish'},
    {'code': 'fr-FR', 'name': 'French'},
    {'code': 'de-DE', 'name': 'German'},
    {'code': 'it-IT', 'name': 'Italian'},
    {'code': 'ja-JP', 'name': 'Japanese'},
    {'code': 'ko-KR', 'name': 'Korean'},
    {'code': 'zh-CN', 'name': 'Chinese (Simplified)'},
    {'code': 'zh-TW', 'name': 'Chinese (Traditional)'},
  ];

  // Available color options
  final List<Color> _availableColors = [
    Colors.yellow,
    Colors.white,
    Colors.orange,
    Colors.pink,
    Colors.purple,
    Colors.blue,
    Colors.green,
    Colors.red,
  ];

  @override
  void initState() {
    super.initState();

    // Get the accessibility service
    _accessibilityService = ref.read(arAccessibilityServiceProvider);

    // Initialize local state
    _isHighContrastEnabled = _accessibilityService.isHighContrastEnabled;
    _isLargeTextEnabled = _accessibilityService.isLargeTextEnabled;
    _isScreenReaderEnabled = _accessibilityService.isScreenReaderEnabled;
    _isReducedMotionEnabled = _accessibilityService.isReducedMotionEnabled;
    _isHapticFeedbackEnabled = _accessibilityService.isHapticFeedbackEnabled;
    _isAudioGuidanceEnabled = _accessibilityService.isAudioGuidanceEnabled;
    _isGestureSimplificationEnabled =
        _accessibilityService.isGestureSimplificationEnabled;

    _speechRate = _accessibilityService.speechRate;
    _speechPitch = _accessibilityService.speechPitch;
    _speechLanguage = _accessibilityService.speechLanguage;

    _primaryAccessibilityColor =
        _accessibilityService.primaryAccessibilityColor;
    _secondaryAccessibilityColor =
        _accessibilityService.secondaryAccessibilityColor;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accessibility Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            tooltip: 'Help',
            onPressed: _showHelpDialog,
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Vision settings
          _buildSectionHeader('Vision Settings'),
          _buildSwitchTile(
            title: 'High Contrast Mode',
            subtitle: 'Increase contrast for better visibility',
            value: _isHighContrastEnabled,
            onChanged: (value) {
              setState(() {
                _isHighContrastEnabled = value;
              });
            },
          ),
          _buildSwitchTile(
            title: 'Large Text',
            subtitle: 'Increase text size for better readability',
            value: _isLargeTextEnabled,
            onChanged: (value) {
              setState(() {
                _isLargeTextEnabled = value;
              });
            },
          ),
          _buildColorSelector(
            title: 'Primary Color',
            subtitle: 'Select the primary color for high contrast mode',
            selectedColor: _primaryAccessibilityColor,
            onColorSelected: (color) {
              setState(() {
                _primaryAccessibilityColor = color;
              });
            },
          ),
          _buildColorSelector(
            title: 'Background Color',
            subtitle: 'Select the background color for high contrast mode',
            selectedColor: _secondaryAccessibilityColor,
            onColorSelected: (color) {
              setState(() {
                _secondaryAccessibilityColor = color;
              });
            },
          ),
          const SizedBox(height: 24),

          // Audio settings
          _buildSectionHeader('Audio Settings'),
          _buildSwitchTile(
            title: 'Screen Reader',
            subtitle: 'Read screen content aloud',
            value: _isScreenReaderEnabled,
            onChanged: (value) {
              setState(() {
                _isScreenReaderEnabled = value;
              });
            },
          ),
          _buildSwitchTile(
            title: 'Audio Guidance',
            subtitle: 'Provide audio instructions for AR navigation',
            value: _isAudioGuidanceEnabled,
            onChanged: (value) {
              setState(() {
                _isAudioGuidanceEnabled = value;
              });
            },
          ),
          _buildSliderTile(
            title: 'Speech Rate',
            subtitle: 'Adjust the speed of speech',
            value: _speechRate,
            min: 0.5,
            max: 2.0,
            divisions: 15,
            label: '${_speechRate.toStringAsFixed(1)}x',
            onChanged: (value) {
              setState(() {
                _speechRate = value;
              });
            },
          ),
          _buildSliderTile(
            title: 'Speech Pitch',
            subtitle: 'Adjust the pitch of speech',
            value: _speechPitch,
            min: 0.5,
            max: 2.0,
            divisions: 15,
            label: '${_speechPitch.toStringAsFixed(1)}x',
            onChanged: (value) {
              setState(() {
                _speechPitch = value;
              });
            },
          ),
          _buildLanguageSelector(),
          const SizedBox(height: 24),

          // Motion settings
          _buildSectionHeader('Motion Settings'),
          _buildSwitchTile(
            title: 'Reduced Motion',
            subtitle: 'Minimize animations and motion effects',
            value: _isReducedMotionEnabled,
            onChanged: (value) {
              setState(() {
                _isReducedMotionEnabled = value;
              });
            },
          ),
          _buildSwitchTile(
            title: 'Haptic Feedback',
            subtitle: 'Provide vibration feedback for interactions',
            value: _isHapticFeedbackEnabled,
            onChanged: (value) {
              setState(() {
                _isHapticFeedbackEnabled = value;
              });
            },
          ),
          const SizedBox(height: 24),

          // Interaction settings
          _buildSectionHeader('Interaction Settings'),
          _buildSwitchTile(
            title: 'Simplified Gestures',
            subtitle: 'Use simpler gestures for AR interactions',
            value: _isGestureSimplificationEnabled,
            onChanged: (value) {
              setState(() {
                _isGestureSimplificationEnabled = value;
              });
            },
          ),
          const SizedBox(height: 24),

          // Preview
          _buildSectionHeader('Preview'),
          _buildPreview(),
          const SizedBox(height: 32),

          // Save button
          ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Save Settings'),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const Divider(),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        value: value,
        onChanged: onChanged,
        secondary: Icon(
          value ? Icons.check_circle : Icons.circle_outlined,
          color: value ? Theme.of(context).primaryColor : null,
        ),
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required String label,
    required ValueChanged<double> onChanged,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 4),
            Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
            Slider(
              value: value,
              min: min,
              max: max,
              divisions: divisions,
              label: label,
              onChanged: onChanged,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Slow', style: Theme.of(context).textTheme.bodySmall),
                Text('Fast', style: Theme.of(context).textTheme.bodySmall),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSelector({
    required String title,
    required String subtitle,
    required Color selectedColor,
    required ValueChanged<Color> onColorSelected,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 4),
            Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: _availableColors.map((color) {
                final isSelected = selectedColor == color;
                return GestureDetector(
                  onTap: () => onColorSelected(color),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey,
                        width: isSelected ? 3 : 1,
                      ),
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            color: color.computeLuminance() > 0.5
                                ? Colors.black
                                : Colors.white,
                          )
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Speech Language',
                style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 4),
            Text('Select the language for speech output',
                style: Theme.of(context).textTheme.bodySmall),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _speechLanguage,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: _availableLanguages.map((language) {
                return DropdownMenuItem<String>(
                  value: language['code'],
                  child: Text(language['name']!),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _speechLanguage = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreview() {
    // Apply the selected accessibility settings to the preview
    final textStyle = TextStyle(
      fontSize: _isLargeTextEnabled ? 18 : 14,
      color: _isHighContrastEnabled ? _primaryAccessibilityColor : null,
      backgroundColor:
          _isHighContrastEnabled ? _secondaryAccessibilityColor : null,
    );

    final containerColor = _isHighContrastEnabled
        ? _secondaryAccessibilityColor
        : Colors.grey[200];

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: containerColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'This is a preview of your accessibility settings.',
                    style: textStyle,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          if (_isHapticFeedbackEnabled) {
                            _accessibilityService.performHapticFeedback(
                                HapticFeedbackType.light);
                          }

                          if (_isScreenReaderEnabled ||
                              _isAudioGuidanceEnabled) {
                            _accessibilityServiceeak('Button pressed');
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          padding: _isLargeTextEnabled
                              ? const EdgeInsets.all(16)
                              : null,
                          textStyle: TextStyle(
                              fontSize: _isLargeTextEnabled ? 18 : 14),
                        ),
                        child: const Text('Test Button'),
                      ),
                      const SizedBox(width: 16),
                      IconButton(
                        onPressed: () {
                          if (_isHapticFeedbackEnabled) {
                            _accessibilityService.performHapticFeedback(
                                HapticFeedbackType.light);
                          }

                          if (_isScreenReaderEnabled ||
                              _isAudioGuidanceEnabled) {
                            _accessibilityServiceeak('Icon button pressed');
                          }
                        },
                        icon: Icon(
                          Icons.info,
                          size: _isLargeTextEnabled ? 36 : 24,
                          color: _isHighContrastEnabled
                              ? _primaryAccessibilityColor
                              : null,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Accessibility Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Vision Settings',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                  '• High Contrast Mode: Increases contrast for better visibility'),
              Text('• Large Text: Increases text size for better readability'),
              Text('• Primary Color: The main color used for text and icons'),
              Text('• Background Color: The color used for backgrounds'),
              SizedBox(height: 16),
              Text(
                'Audio Settings',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Screen Reader: Reads screen content aloud'),
              Text(
                  '• Audio Guidance: Provides audio instructions for AR navigation'),
              Text('• Speech Rate: Controls how fast the speech is'),
              Text(
                  '• Speech Pitch: Controls how high or low the speech sounds'),
              Text('• Speech Language: The language used for speech'),
              SizedBox(height: 16),
              Text(
                'Motion Settings',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Reduced Motion: Minimizes animations and motion effects'),
              Text(
                  '• Haptic Feedback: Provides vibration feedback for interactions'),
              SizedBox(height: 16),
              Text(
                'Interaction Settings',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                  '• Simplified Gestures: Uses simpler gestures for AR interactions'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    // Update the accessibility service with the new settings
    _accessibilityService.toggleHighContrast(_isHighContrastEnabled);
    _accessibilityService.toggleLargeText(_isLargeTextEnabled);
    _accessibilityService.toggleScreenReader(_isScreenReaderEnabled);
    _accessibilityService.toggleReducedMotion(_isReducedMotionEnabled);
    _accessibilityService.toggleHapticFeedback(_isHapticFeedbackEnabled);
    _accessibilityService.toggleAudioGuidance(_isAudioGuidanceEnabled);
    _accessibilityService
        .toggleGestureSimplification(_isGestureSimplificationEnabled);

    _accessibilityService.setSpeechRate(_speechRate);
    _accessibilityService.setSpeechPitch(_speechPitch);
    _accessibilityService.setSpeechLanguage(_speechLanguage);

    _accessibilityService
        .setPrimaryAccessibilityColor(_primaryAccessibilityColor);
    _accessibilityService
        .setSecondaryAccessibilityColor(_secondaryAccessibilityColor);

    // Show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Accessibility settings saved'),
        duration: Duration(seconds: 2),
      ),
    );

    // Navigate back
    Navigator.pop(context);
  }
}
