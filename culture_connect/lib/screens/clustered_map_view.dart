import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/cluster_service.dart';
import 'package:culture_connect/services/marker_service.dart';
import 'package:culture_connect/widgets/custom_info_window.dart';

class ClusteredMapView extends StatefulWidget {
  final List<Experience> experiences;
  final Function(Experience) onExperienceSelected;
  final CameraPosition initialCameraPosition;
  final bool showUserLocation;
  final bool showLocationButton;

  const ClusteredMapView({
    super.key,
    required this.experiences,
    required this.onExperienceSelected,
    this.initialCameraPosition = const CameraPosition(
      target: LatLng(6.5244, 3.3792), // Lagos coordinates
      zoom: 12,
    ),
    this.showUserLocation = true,
    this.showLocationButton = true,
  });

  @override
  State<ClusteredMapView> createState() => _ClusteredMapViewState();
}

class _ClusteredMapViewState extends State<ClusteredMapView> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  CameraPosition? _currentCameraPosition;
  double _currentZoomLevel = 12.0;
  Experience? _selectedExperience;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GoogleMap(
          initialCameraPosition: widget.initialCameraPosition,
          markers: _markers,
          onMapCreated: (controller) {
            _mapController = controller;
          },
          onCameraMove: (position) {
            _currentCameraPosition = position;
            _currentZoomLevel = position.zoom;
            if (_selectedExperience != null) {
              setState(() {
                _selectedExperience = null;
              });
            }
          },
          onCameraIdle: () {
            if (_currentCameraPosition != null) {
              _updateMarkers(widget.experiences);
            }
          },
          myLocationEnabled: widget.showUserLocation,
          myLocationButtonEnabled: widget.showLocationButton,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
        ),
        if (_selectedExperience != null)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: CustomInfoWindow(
              experience: _selectedExperience!,
              onTap: () => widget.onExperienceSelected(_selectedExperience!),
            ),
          ),
      ],
    );
  }

  Future<void> _updateMarkers(List<Experience> experiences) async {
    if (_currentCameraPosition == null) return;

    final clusters = ClusterService.createClusters(
      experiences,
      _currentCameraPosition!,
      _currentZoomLevel,
    );

    final markers = <Marker>{};

    for (final cluster in clusters) {
      if (cluster.isMultiple) {
        // Create cluster marker
        final marker = Marker(
          markerId: MarkerId(
              'cluster_${cluster.position.latitude}_${cluster.position.longitude}'),
          position: cluster.position,
          icon: await _createClusterIcon(cluster.count),
          onTap: () {
            // Zoom in when cluster is tapped
            _mapController?.animateCamera(
              CameraUpdate.newLatLngZoom(
                cluster.position,
                _currentZoomLevel + 1,
              ),
            );
          },
        );
        markers.add(marker);
      } else {
        // Create individual experience marker
        final experience = cluster.experiences.first;
        final marker = await MarkerService.createMarker(
          experience,
          onTap: () {
            setState(() {
              _selectedExperience = experience;
            });
          },
        );
        markers.add(marker);
      }
    }

    setState(() {
      _markers.clear();
      _markers.addAll(markers);
    });
  }

  Future<BitmapDescriptor> _createClusterIcon(int count) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    const size = Size(48, 48);
    final paint = Paint()
      ..color = Colors.blue.withAlpha(230)
      ..style = PaintingStyle.fill;

    // Draw circle
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width / 2,
      paint,
    );

    // Draw border
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width / 2,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );

    // Draw text
    final textPainter = TextPainter(
      text: TextSpan(
        text: count.toString(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        (size.height - textPainter.height) / 2,
      ),
    );

    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.toInt(),
      size.height.toInt(),
    );
    final bytes = await image.toByteData(format: ui.ImageByteFormat.png);

    return BitmapDescriptor.fromBytes(bytes!.buffer.asUint8List());
  }
}
