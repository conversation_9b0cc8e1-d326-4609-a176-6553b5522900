import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';

import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/providers/ar_voice_command_provider.dart';
import 'package:culture_connect/providers/ar_backend_provider.dart';
import 'package:culture_connect/providers/ar_recording_provider.dart';
import 'package:culture_connect/providers/ar_lazy_loading_provider.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart'
    hide arVoiceCommandServiceProvider;
import 'package:culture_connect/services/ar_backend_service.dart'
    hide arBackendServiceProvider;
import 'package:culture_connect/services/ar_recording_service.dart'
    hide arRecordingServiceProvider;
import 'package:culture_connect/services/ar_lazy_loading_service.dart';
import 'package:culture_connect/widgets/ar_voice_command_ui.dart';
import 'package:culture_connect/widgets/ar_recording_controls.dart';
import 'package:culture_connect/screens/ar/ar_settings_screen.dart';

/// AR Explore Screen with interactive elements, performance optimization,
/// and content management features.
class ARExploreScreen extends ConsumerStatefulWidget {
  final Landmark? initialLandmark;
  final bool startNavigation;

  const ARExploreScreen({
    super.key,
    this.initialLandmark,
    this.startNavigation = false,
  });

  @override
  ConsumerState<ARExploreScreen> createState() => _ARExploreScreenState();
}

class _ARExploreScreenState extends ConsumerState<ARExploreScreen>
    with TickerProviderStateMixin {
  ArCoreController? _arController;
  late ARVoiceCommandService _voiceCommandService;
  late ARBackendService _arBackendService;
  late ARRecordingService _recordingService;
  late ARLazyLoadingService _arLazyLoadingService;
  bool _isLoading = true;
  String? _error;
  bool _isNavigating = false;
  Landmark? _selectedLandmark;
  bool _showVoiceCommandUI = false;
  bool _showRecordingControls = false;
  bool _arFeaturesLoaded = false;

  // Landmarks from backend
  List<Landmark> _nearbyLandmarks = [];

  // Animation controllers
  late AnimationController _pulseAnimationController;
  late AnimationController _fadeAnimationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  // UI state
  bool _showPerformanceOverlay = false;
  bool _showDebugInfo = false;
  bool _showSettings = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _pulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _fadeAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
          parent: _pulseAnimationController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(
          parent: _fadeAnimationController, curve: Curves.easeInOut),
    );

    // Initialize services
    _voiceCommandService = ref.read(arVoiceCommandServiceProvider);
    _arBackendService = ref.read(arBackendServiceProvider);
    _recordingService = ref.read(arRecordingServiceProvider);
    _arLazyLoadingService = ref.read(arLazyLoadingServiceProvider);

    _initializeVoiceCommands();
    _initializeBackendService();
    _initializeRecordingService();
    _initializeARLazyLoading();

    _initializeAR();

    if (widget.initialLandmark != null && widget.startNavigation) {
      _startNavigation(widget.initialLandmark!);
    }
  }

  Future<void> _initializeBackendService() async {
    try {
      await _arBackendService.initialize();
      _fetchNearbyLandmarks();
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize AR backend service: $e';
      });
    }
  }

  Future<void> _initializeRecordingService() async {
    try {
      await _recordingService.initialize(
        onRecordingStateChanged: () {
          setState(() {});
        },
        onRecordingTimerUpdated: (duration) {
          setState(() {});
        },
      );
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize AR recording service: $e';
      });
    }
  }

  Future<void> _initializeARLazyLoading() async {
    try {
      // Only initialize AR features when needed, not during startup
      // This will be triggered when the user navigates to the AR screen
      _arFeaturesLoaded = _arLazyLoadingService.arFeaturesLoaded;

      // If AR features are not loaded, listen for loading completion
      if (!_arFeaturesLoaded) {
        _arLazyLoadingService.arFeaturesLoadingFuture.then((loaded) {
          setState(() {
            _arFeaturesLoaded = loaded;
          });
        }).catchError((e) {
          setState(() {
            _error = 'Failed to load AR features: $e';
          });
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize AR lazy loading service: $e';
      });
    }
  }

  void _toggleRecordingControls() {
    setState(() {
      _showRecordingControls = !_showRecordingControls;
    });
  }

  void _handleRecordingStateChanged() {
    setState(() {});
  }

  void _handleRecordingShared() {
    setState(() {
      _showRecordingControls = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR experience shared successfully'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleRecordingSaved() {
    setState(() {
      _showRecordingControls = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR experience saved successfully'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleRecordingDiscarded() {
    setState(() {
      _showRecordingControls = false;
    });
  }

  Future<void> _fetchNearbyLandmarks() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Mock location for demo purposes
      final landmarks = await _arBackendService.getLandmarks(
        latitude: 37.7749,
        longitude: -122.4194,
        radius: 1000,
      );

      setState(() {
        _nearbyLandmarks = landmarks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to fetch nearby landmarks: $e';
        _isLoading = false;
      });
    }
  }

  void _initializeVoiceCommands() async {
    await _voiceCommandService.initialize();

    // Register command handlers
    _voiceCommandService.registerCommands({
      'zoom in': _zoomIn,
      'zoom out': _zoomOut,
      'rotate left': _rotateLeft,
      'rotate right': _rotateRight,
      'show info': _showLandmarkInfo,
      'hide info': _hideLandmarkInfo,
      'take photo': _takeScreenshot,
      'show settings': _toggleSettings,
      'show map': _showMap,
      'hide map': _hideMap,
      'navigate to': _startNavigation,
      'stop navigation': _stopNavigation,
      'help': _toggleVoiceCommandUI,
      'go back': _goBack,
    });
  }

  Future<void> _initializeAR() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Check if AR features are loaded
      if (!_arFeaturesLoaded) {
        // Load AR features lazily
        final loadingStartTime = DateTime.now();
        final success = await _arLazyLoadingService.initializeARFeatures();
        final loadingDuration = DateTime.now().difference(loadingStartTime);

        // Log loading time
        debugPrint('AR features loaded in ${loadingDuration.inMilliseconds}ms');

        if (!success) {
          throw Exception('Failed to load AR features');
        }

        _arFeaturesLoaded = true;
      }

      // In a real implementation, this would initialize AR Core
      // For demo purposes, we'll just simulate loading
      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to initialize AR: $e';
      });
    }
  }

  // This method is called when the AR view is created
  // It's referenced in the ArCoreView widget in a real implementation
  // For now, we'll keep it as a placeholder for future implementation

  // This method sets up the AR scene with tap handlers
  // It's called from _onArCoreViewCreated in a real implementation
  // For now, we'll keep it as a placeholder for future implementation

  // These methods are used for AR landmark functionality
  // They're called from the AR view in a real implementation
  // For now, we'll keep them as placeholders for future implementation

  Future<void> _showLandmarkDetails(Landmark landmark) async {
    setState(() {
      _selectedLandmark = landmark;
      _isLoading = true;
    });

    String? successMessage;
    String? errorMessage;

    try {
      // Fetch AR model from backend
      final arModel = await _arBackendService.getARModel(landmark.id);

      if (arModel != null) {
        // Download AR model file if needed
        final modelUrl = landmark.arContent['modelUrl'] as String;
        final modelFile = await _arBackendService.downloadARModelFile(modelUrl);

        if (modelFile != null) {
          successMessage = 'AR model loaded for ${landmark.name}';
        }
      }
    } catch (e) {
      errorMessage = 'Failed to load AR model: $e';
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show success or error message if needed
        if (successMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              duration: const Duration(seconds: 2),
            ),
          );
        } else if (errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              duration: const Duration(seconds: 2),
            ),
          );
        }

        // Show details in a bottom sheet
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => _buildLandmarkDetailsSheet(landmark),
        );
      }
    }
  }

  Widget _buildLandmarkDetailsSheet(Landmark landmark) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header image
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
              image: DecorationImage(
                image: NetworkImage(landmark.imageUrl),
                fit: BoxFit.cover,
              ),
            ),
            child: Stack(
              children: [
                // Gradient overlay
                Container(
                  decoration: BoxDecoration(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(20)),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.blackithAlpha(180),
                      ],
                    ),
                  ),
                ),

                // Title and rating
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        landmark.name,
                        style: const TextStyle(
                          color: Colorshite,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 18,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${landmarkating} (${landmarkeviewCount} reviews)',
                            style: const TextStyle(
                              color: Colorshite,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Close button
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.blackithAlpha(128),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close, color: Colorshite),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description
                  Text(
                    landmark.description,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 16),

                  // Historical significance
                  Text(
                    'Historical Significance',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    landmarkistoricalSignificance,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),

                  // Tags
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: landmark.tags
                        .map((tag) => Chip(
                              label: Text(tag),
                              backgroundColor: Theme.of(context)
                                  .colorScheme
                                  .surfaceContainerHighest,
                            ))
                        .toList(),
                  ),
                  const SizedBox(height: 24),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _startNavigation(landmark);
                          },
                          icon: const Icon(Icons.directions),
                          label: const Text('Navigate in AR'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colorshite,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _triggerAnimation(
                                landmark.id, AnimationType.rotate);
                          },
                          icon: const Icon(Icons.view_in_ar),
                          label: const Text('View in 3D'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _startNavigation(Landmark landmark) {
    setState(() {
      _isNavigating = true;
      _selectedLandmark = landmark;
    });

    // Show navigation UI
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigation started to ${landmark.name}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _stopNavigation() {
    setState(() {
      _isNavigating = false;
      _selectedLandmark = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigation stopped'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // These methods are used for AR navigation functionality
  // They're called from the AR view in a real implementation
  // For now, we'll keep them as placeholders for future implementation

  void _triggerAnimation(String landmarkId, AnimationType type) {
    // For demo purposes, we'll just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Triggered ${type.toString().split('.').last} animation on landmark $landmarkId'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _togglePerformanceOverlay() {
    setState(() {
      _showPerformanceOverlay = !_showPerformanceOverlay;
    });
  }

  void _toggleSettings() {
    // Navigate to the AR settings screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ARSettingsScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isNavigating ? 'AR Navigation' : 'AR Explore'),
        actions: [
          IconButton(
            icon: const Icon(Icons.mic),
            onPressed: _toggleVoiceCommandUI,
            tooltip: 'Voice Commands',
          ),
          IconButton(
            icon: Icon(_recordingService.isRecording
                ? Icons.stop_circle
                : Icons.videocam),
            onPressed: _toggleRecordingControls,
            tooltip: _recordingService.isRecording
                ? 'Stop Recording'
                : 'Record AR Experience',
            color: _recordingService.isRecording ? Colorsed : null,
          ),
          if (_isNavigating)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _stopNavigation,
            ),
          IconButton(
            icon: const Icon(Iconseed),
            onPressed: _togglePerformanceOverlay,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _toggleSettings,
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (_voiceCommandService.isListening) {
            _voiceCommandService.stopListening();
          } else {
            _voiceCommandService.startListening();
          }
        },
        backgroundColor: _voiceCommandService.isListening
            ? Colorsed
            : Theme.of(context).primaryColor,
        tooltip: _voiceCommandService.isListening
            ? 'Stop Listening'
            : 'Start Listening',
        child: Icon(
          _voiceCommandService.isListening ? Icons.mic_off : Icons.mic,
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colorsed),
            const SizedBox(height: 16),
            Text(_error!, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeAR,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // For demo purposes, we'll show a placeholder
    return Stack(
      children: [
        // AR View placeholder
        Container(
          color: Colors.black,
          child: Center(
            child: Text(
              'AR View',
              style: Theme.of(context).textThemeeadlineMedium?.copyWith(
                    color: Colorshite,
                  ),
            ),
          ),
        ),

        // Performance overlay
        if (_showPerformanceOverlay) _buildPerformanceOverlay(),

        // Debug info
        if (_showDebugInfo) _buildDebugInfo(),

        // Settings panel
        if (_showSettings) _buildSettingsPanel(),

        // Navigation UI
        if (_isNavigating) _buildNavigationUI(),

        // Nearby landmarks panel
        if (!_isNavigating && !_showRecordingControls)
          _buildNearbyLandmarksPanel(),

        // Recording controls
        if (_showRecordingControls)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: ARRecordingControls(
              onRecordingStateChanged: _handleRecordingStateChanged,
              onRecordingShared: _handleRecordingShared,
              onRecordingSaved: _handleRecordingSaved,
              onRecordingDiscarded: _handleRecordingDiscarded,
            ),
          ),

        // Voice command UI
        if (_showVoiceCommandUI)
          Positioned(
            top: 80,
            left: 16,
            right: 16,
            child: ARVoiceCommandUI(
              onClose: _toggleVoiceCommandUI,
            ),
          ),

        // Voice recognition status
        if (_voiceCommandService.isListening)
          Positioned(
            bottom: _isNavigating || !_isNavigating ? 150 : 16,
            right: 16,
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorsedithAlpha(200),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.mic,
                    color: Colorshite,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Listening...',
                    style: TextStyle(
                      color: Colorshite,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Recording status
        if (_recordingService.isRecording)
          Positioned(
            top: 80,
            right: 16,
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorsedithAlpha(200),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.videocam,
                    color: Colorshite,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _recordingService.isPaused
                        ? 'Paused: ${_recordingServiceecordingDurationFormatted}'
                        : 'Recording: ${_recordingServiceecordingDurationFormatted}',
                    style: const TextStyle(
                      color: Colorshite,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPerformanceOverlay() {
    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blackithAlpha(180),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance',
              style: TextStyle(
                color: Colorshite,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FPS: 45.2',
              style: TextStyle(color: Colorshite),
            ),
            const Text(
              'Memory: 120.5 MB',
              style: TextStyle(color: Colorshite),
            ),
            const Text(
              'Nodes: 5',
              style: TextStyle(color: Colorshite),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _showDebugInfo = !_showDebugInfo;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _showDebugInfo ? Colors.green : Colors.grey,
                foregroundColor: Colorshite,
                minimumSize: const Size(double.infinity, 36),
              ),
              child:
                  Text(_showDebugInfo ? 'Hide Debug Info' : 'Show Debug Info'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugInfo() {
    return Positioned(
      top: 16,
      left: 16,
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blackithAlpha(180),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Debug Info',
              style: TextStyle(
                color: Colorshite,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Landmarks: 2',
              style: TextStyle(color: Colorshite),
            ),
            Text(
              'Content Cached: Yes',
              style: TextStyle(color: Colorshite),
            ),
            Text(
              'Offline Mode: No',
              style: TextStyle(color: Colorshite),
            ),
            Text(
              'Interactive: Yes',
              style: TextStyle(color: Colorshite),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsPanel() {
    return Positioned(
      top: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colorshite,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.blackithAlpha(51),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignmentaceBetween,
              children: [
                Text(
                  'AR Settings',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _toggleSettings,
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),

            // Quality settings
            Text(
              'Quality',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _updateQualitySettings('low'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                    ),
                    child: const Text('Low'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _updateQualitySettings('medium'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colorshite,
                    ),
                    child: const Text('Medium'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _updateQualitySettings('high'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                    ),
                    child: const Text('High'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Cache settings
            Text(
              'Content Cache',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearARCache,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colorsed,
                      foregroundColor: Colorshite,
                    ),
                    child: const Text('Clear Cache'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationUI() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ColorshiteithAlpha(230),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.blackithAlpha(51),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: const Icon(
                        Icons.navigation,
                        color: Colors.blue,
                        size: 24,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
                Text(
                  'Navigation Mode',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (_selectedLandmark != null)
              Text(
                'Navigating to: ${_selectedLandmark!.name}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            const SizedBox(height: 8),
            Text(
              'Tap on the ground to add navigation points',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _stopNavigation,
                    icon: const Icon(Icons.close),
                    label: const Text('End Navigation'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colorshite,
                      foregroundColor: Colorsed,
                      side: const BorderSide(color: Colorsed),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNearbyLandmarksPanel() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ColorshiteithAlpha(230),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.blackithAlpha(51),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignmentaceBetween,
              children: [
                Text(
                  'Nearby Landmarks',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                if (_nearbyLandmarks.isNotEmpty)
                  Text(
                    '${_nearbyLandmarks.length} found',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_error != null)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colorsed,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        style: const TextStyle(color: Colorsed),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _fetchNearbyLandmarks,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              )
            else if (_nearbyLandmarks.isEmpty)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.location_off,
                        color: Colors.grey,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'No landmarks found nearby',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _fetchNearbyLandmarks,
                        child: const Text('Refresh'),
                      ),
                    ],
                  ),
                ),
              )
            else
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axisorizontal,
                  itemCount: _nearbyLandmarks.length,
                  itemBuilder: (context, index) {
                    final landmark = _nearbyLandmarks[index];

                    return GestureDetector(
                      onTap: () => _showLandmarkDetails(landmark),
                      child: Container(
                        width: 100,
                        margin: EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey[300],
                        ),
                        child: Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.blackithAlpha(180),
                                  ],
                                ),
                              ),
                              padding: EdgeInsets.all(8),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AnimatedBuilder(
                                    animation: _fadeAnimation,
                                    builder: (context, child) {
                                      return Opacity(
                                        opacity: _fadeAnimation.value,
                                        child: child,
                                      );
                                    },
                                    child: Text(
                                      landmark.name,
                                      style: const TextStyle(
                                        color: Colorshite,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Backend indicator
                            Positioned(
                              top: 4,
                              right: 4,
                              child: Container(
                                padding: EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.cloud_done,
                                  color: Colorshite,
                                  size: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _updateQualitySettings(String quality) {
    // For demo purposes, we'll just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Quality set to $quality'),
        duration: const Duration(seconds: 2),
      ),
    );

    setState(() {
      _showSettings = false;
    });
  }

  void _clearARCache() {
    // For demo purposes, we'll just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR cache cleared'),
        duration: Duration(seconds: 2),
      ),
    );

    setState(() {
      _showSettings = false;
    });
  }

  // Voice command methods
  void _toggleVoiceCommandUI() {
    setState(() {
      _showVoiceCommandUI = !_showVoiceCommandUI;
    });
  }

  void _zoomIn() {
    // In a real implementation, this would zoom in the AR view
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Zooming in'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _zoomOut() {
    // In a real implementation, this would zoom out the AR view
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Zooming out'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _rotateLeft() {
    // In a real implementation, this would rotate the AR model left
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rotating left'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _rotateRight() {
    // In a real implementation, this would rotate the AR model right
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rotating right'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showLandmarkInfo() {
    // Show landmark info
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Showing landmark info'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _hideLandmarkInfo() {
    // Hide landmark info
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Hiding landmark info'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _takeScreenshot() {
    // In a real implementation, this would take a screenshot of the AR view
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Screenshot taken'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showMap() {
    // In a real implementation, this would show a map overlay
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Showing map'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _hideMap() {
    // In a real implementation, this would hide the map overlay
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Hiding map'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _goBack() {
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _arController?.dispose();
    _pulseAnimationController.dispose();
    _fadeAnimationController.dispose();
    _voiceCommandService.dispose();
    super.dispose();
  }
}
